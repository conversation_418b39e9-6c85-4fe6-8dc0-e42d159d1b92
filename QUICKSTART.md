# 🚀 Быстрый старт PDF Compressor

## Что это такое?

Автоматический компрессор PDF файлов, который:
- 🗂️ Работает с облачным хранилищем Mega
- 🤖 Использует бесплатные GitHub Actions  
- 🗜️ Сжимает PDF файлы до 90% от исходного размера
- 📱 Отправляет отчеты в Telegram

## Минимальная настройка (5 минут)

### 1. Fork репозитория
Нажмите "Fork" в GitHub или скачайте файлы

### 2. Добавьте секреты
В настройках GitHub репозитория → Secrets and variables → Actions:

```
MEGA_EMAIL = ваш<EMAIL>
MEGA_PASSWORD = ваш-пароль-mega
```

### 3. Создайте папки в Mega
```
/PDF/Input/      ← сюда кладете файлы для сжатия
/PDF/Compressed/ ← сюда попадают сжатые файлы
```

### 4. Запустите!
- Actions → "Manual PDF Compression" → "Run workflow"
- Или просто положите PDF в `/PDF/Input/` и ждите автоматического запуска

## Результат

✅ PDF файлы автоматически сжимаются  
✅ Оригиналы заменяются сжатыми версиями  
✅ Отчет о результатах в Actions  

## Telegram уведомления (опционально)

1. Создайте бота: [@BotFather](https://t.me/botfather)
2. Узнайте Chat ID: [@userinfobot](https://t.me/userinfobot)
3. Добавьте в GitHub Secrets:
   ```
   TELEGRAM_BOT_TOKEN = токен-от-BotFather
   TELEGRAM_CHAT_ID = ваш-chat-id
   ```

## Локальный запуск

```bash
# 1. Установка
./install.sh

# 2. Настройка
cp .env.example .env
nano .env  # добавьте ваши данные Mega

# 3. Запуск
python3 src/main.py --source "/PDF/Input" --target "/PDF/Output"
```

## Настройка расписания

Отредактируйте `.github/workflows/compress-pdfs.yml`:

```yaml
schedule:
  - cron: '0 2 * * *'    # каждый день в 2:00 UTC
  - cron: '0 14 * * *'   # каждый день в 14:00 UTC
```

## Уровни сжатия

- **low**: минимальные потери качества (85% качество изображений)
- **medium**: баланс размера/качества (75% качество) - по умолчанию  
- **high**: максимальное сжатие (60% качество, для веб)

## Типичные результаты

| Тип файла | Экономия места |
|-----------|----------------|
| Сканы документов | 70-90% |
| PDF с изображениями | 50-85% |  
| Текстовые PDF | 20-60% |

## Проблемы?

1. **Не работает Mega**: проверьте email/пароль в секретах
2. **Превышен лимит времени**: уменьшите количество файлов за раз
3. **Файлы не сжимаются**: возможно, уже сжаты или повреждены

## Полная документация

Смотрите [README.md](README.md) для детальной настройки и возможностей.

---

**🗜️ Экономьте место в облаке автоматически!**
