# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
temp/
tmp/
*.tmp
*.bak

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# Secrets (важно!)
config/secrets.yaml
.env
*.key
*.pem

# Mega cache
.mega_session
mega_cache/

# Compressed files storage
output/
processed/

# GitHub Actions artifacts
artifacts/

# Test files
test_*.pdf
sample_*.pdf

# Local configuration overrides
config/local.yaml
config/production.yaml

# Database files
*.db
*.sqlite
*.sqlite3

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env.local
.env.development
.env.test
.env.production

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
