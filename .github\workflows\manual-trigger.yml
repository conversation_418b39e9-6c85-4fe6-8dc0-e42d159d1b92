name: Manual PDF Compression

on:
  workflow_dispatch:
    inputs:
      source_folder:
        description: 'Source folder in Mega'
        required: true
        default: '/PDF/Input'
        type: string
      target_folder:
        description: 'Target folder in Mega'
        required: true
        default: '/PDF/Compressed'
        type: string
      compression_level:
        description: 'Compression level'
        required: true
        default: 'medium'
        type: choice
        options:
          - low
          - medium
          - high
      max_files:
        description: 'Maximum files to process'
        required: true
        default: '50'
        type: string
      file_pattern:
        description: 'File pattern to match (e.g., *.pdf, report_*.pdf)'
        required: false
        default: '*.pdf'
        type: string
      create_backup:
        description: 'Create backup before compression'
        required: false
        default: true
        type: boolean
      verify_files:
        description: 'Verify compressed files integrity'
        required: false
        default: true
        type: boolean
      delete_originals:
        description: 'Delete original files after compression'
        required: false
        default: true
        type: boolean
      dry_run:
        description: 'Test run (no actual changes)'
        required: false
        default: false
        type: boolean
      send_notification:
        description: 'Send Telegram notification'
        required: false
        default: true
        type: boolean

env:
  MEGA_EMAIL: ${{ secrets.MEGA_EMAIL }}
  MEGA_PASSWORD: ${{ secrets.MEGA_PASSWORD }}
  TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
  TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}

jobs:
  manual-compression:
    runs-on: ubuntu-latest
    timeout-minutes: 360  # 6 часов
    
    steps:
    - name: 📋 Checkout repository
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        
    - name: 📦 Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y ghostscript qpdf poppler-utils
        pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 📁 Setup workspace
      run: |
        mkdir -p temp/{input,output,logs,backup}
        
    - name: 📊 Pre-run information
      run: |
        echo "## 🎯 Manual Compression Job" >> $GITHUB_STEP_SUMMARY
        echo "**Parameters:**" >> $GITHUB_STEP_SUMMARY
        echo "- Source: \`${{ github.event.inputs.source_folder }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- Target: \`${{ github.event.inputs.target_folder }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- Level: \`${{ github.event.inputs.compression_level }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- Max files: \`${{ github.event.inputs.max_files }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- Pattern: \`${{ github.event.inputs.file_pattern }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- Backup: \`${{ github.event.inputs.create_backup }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- Verify: \`${{ github.event.inputs.verify_files }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- Delete originals: \`${{ github.event.inputs.delete_originals }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- Dry run: \`${{ github.event.inputs.dry_run }}\`" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
    - name: 🔧 Configure settings
      run: |
        # Создаем временный конфигурационный файл с пользовательскими настройками
        python3 << 'EOF'
        import yaml
        import os
        
        # Загружаем базовую конфигурацию
        with open('config/settings.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Применяем пользовательские настройки
        config['safety']['create_backup'] = '${{ github.event.inputs.create_backup }}' == 'true'
        config['safety']['verify_compression'] = '${{ github.event.inputs.verify_files }}' == 'true'
        config['limits']['max_files_per_run'] = int('${{ github.event.inputs.max_files }}')
        
        # Сохраняем временную конфигурацию
        with open('temp/manual_settings.yaml', 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        print("✅ Configuration updated")
        EOF
        
    - name: 🗜️ Run compression
      id: compression
      run: |
        cd src
        
        # Базовые параметры
        ARGS=(
          "--source" "${{ github.event.inputs.source_folder }}"
          "--target" "${{ github.event.inputs.target_folder }}"
          "--level" "${{ github.event.inputs.compression_level }}"
          "--max-files" "${{ github.event.inputs.max_files }}"
          "--log-file" "../temp/logs/manual_compression.log"
          "--log-level" "INFO"
          "--config" "../temp/manual_settings.yaml"
        )
        
        # Добавляем dry-run если нужно
        if [[ "${{ github.event.inputs.dry_run }}" == "true" ]]; then
          ARGS+=("--dry-run")
        fi
        
        # Запускаем компрессию
        echo "🚀 Starting compression with parameters: ${ARGS[@]}"
        python main.py "${ARGS[@]}"
        
        # Сохраняем код возврата
        echo "exit_code=$?" >> $GITHUB_OUTPUT
        
    - name: 📊 Process results
      if: always()
      run: |
        # Генерируем детальный отчет
        python scripts/generate_detailed_report.py \
          temp/logs/manual_compression.log \
          temp/logs/stats.json > temp/detailed_report.md
        
        # Добавляем результаты в step summary
        if [[ -f temp/detailed_report.md ]]; then
          cat temp/detailed_report.md >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: 📱 Send notification
      if: always() && github.event.inputs.send_notification == 'true' && env.TELEGRAM_BOT_TOKEN != ''
      run: |
        # Отправляем подробное уведомление
        python scripts/send_detailed_notification.py \
          temp/detailed_report.md \
          "${{ steps.compression.outputs.exit_code }}" \
          "${{ github.run_id }}" \
          "${{ github.event.inputs.dry_run }}"
        
    - name: 📂 Archive results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: manual-compression-${{ github.run_number }}-${{ github.event.inputs.compression_level }}
        path: |
          temp/logs/
          temp/detailed_report.md
          temp/manual_settings.yaml
        retention-days: 90
        
    - name: 📈 Update statistics
      if: success() && github.event.inputs.dry_run == 'false'
      run: |
        # Обновляем общую статистику проекта
        python scripts/update_project_stats.py temp/logs/stats.json
        
        # Коммитим если есть изменения
        if [[ -n "$(git status --porcelain)" ]]; then
          git config --local user.email "<EMAIL>"
          git config --local user.name "Manual Compression Bot"
          git add -A
          git commit -m "📊 Update stats after manual compression [skip ci]"
          git push
        fi
        
    - name: 🎯 Job summary
      if: always()
      run: |
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "---" >> $GITHUB_STEP_SUMMARY
        echo "## 📋 Job Completion" >> $GITHUB_STEP_SUMMARY
        
        if [[ "${{ steps.compression.outputs.exit_code }}" == "0" ]]; then
          echo "✅ **Status:** Completed successfully" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Status:** Failed (exit code: ${{ steps.compression.outputs.exit_code }})" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "⏰ **Completed at:** $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "🔗 **Artifacts:** Available for download above" >> $GITHUB_STEP_SUMMARY
        
        if [[ "${{ github.event.inputs.dry_run }}" == "true" ]]; then
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "ℹ️ **Note:** This was a dry run - no actual changes were made" >> $GITHUB_STEP_SUMMARY
        fi
